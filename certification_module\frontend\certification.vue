<template>
  <view class="certification-page">
    <view class="header">
      <text class="title">医师认证</text>
      <text class="subtitle">请填写您的医师信息</text>
    </view>

    <view class="form-section">
      <view class="form-item">
        <text class="label">医师姓名：</text>
        <input v-model="formData.name" placeholder="请输入医师姓名" class="input" />
      </view>

      <view class="form-item">
        <text class="label">医师证号：</text>
        <input v-model="formData.certificateNumber" placeholder="请输入医师证号" class="input" />
      </view>

      <view class="form-item">
        <text class="label">执业医院：</text>
        <input v-model="formData.hospital" placeholder="请输入执业医院" class="input" />
      </view>

      <view class="form-item">
        <text class="label">专业方向：</text>
        <input v-model="formData.specialty" placeholder="请输入专业方向" class="input" />
      </view>

      <view class="form-item">
        <text class="label">医师证照片：</text>
        <image-upload v-model="formData.certificationImage" class="image-upload" />
      </view>

      <view class="form-item" v-if="formData.status === 3">
        <text class="label">拒绝原因：</text>
        <text class="rejection-reason">{{ formData.rejectionReason }}</text>
      </view>
    </view>

    <button class="submit-btn" @click="submitCertification" :disabled="isLoading">
      <text v-if="isLoading">提交中...</text>
      <text v-else>提交认证</text>
    </button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        certificateNumber: '',
        hospital: '',
        specialty: '',
        certificationImage: '',
        status: 0,
        rejectionReason: ''
      },
      isLoading: false
    };
  },

  onLoad() {
    this.checkCertificationStatus();
  },

  methods: {
    async checkCertificationStatus() {
      try {
        const res = await this.$u.get('/api/user/certification');
        if (res.status !== 2) {
          this.formData.status = res.status;
          if (res.status === 3 && res.certificationInfo?.rejectionReason) {
            this.formData.rejectionReason = res.certificationInfo.rejectionReason;
          }
        }
      } catch (error) {
        console.error('获取认证状态失败:', error);
        uni.showToast({
          title: '获取认证状态失败',
          icon: 'error'
        });
      }
    },

    async submitCertification() {
      if (this.isLoading) return;

      // 验证表单
      if (!this.formData.name || !this.formData.certificateNumber || !this.formData.hospital || !this.formData.specialty) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'error'
        });
        return;
      }

      if (!this.formData.certificationImage) {
        uni.showToast({
          title: '请上传医师证照片',
          icon: 'error'
        });
        return;
      }

      this.isLoading = true;
      try {
        const res = await this.$u.post('/api/user/certification', this.formData);
        if (res.code === 200) {
          uni.showToast({
            title: '提交成功，等待审核',
            icon: 'success'
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: res.msg || '提交失败',
            icon: 'error'
          });
        }
      } catch (error) {
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'error'
        });
      } finally {
        this.isLoading = false;
      }
    }
  }
};
</script>

<style scoped>
.certification-page {
  padding: 30rpx;
  background: #fff;
  min-height: 100vh;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
}

.form-section {
  background: #f8f8f8;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.image-upload {
  width: 200rpx;
  height: 200rpx;
}

.rejection-reason {
  color: #ff4444;
  font-size: 28rpx;
  line-height: 1.5;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #007AFF;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-top: 20rpx;
}

.submit-btn:disabled {
  background: #ccc;
}
</style>
