<?php
// +----------------------------------------------------------------------
// | Copyright (c) 2013-2020 http://www.ddt.com All rights reserved.
// +----------------------------------------------------------------------
// | DDT [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Author: yxx <<EMAIL>>
// +----------------------------------------------------------------------
return [
    // 认证状态
    'status' => [
        0 => __('未认证'),
        1 => __('待审核'),
        2 => __('已通过'),
        3 => __('已拒绝')
    ],

    // 认证列表字段
    'index' => [
        'id' => [
            'title' => __('ID'),
            'type' => 'text',
            'search' => true
        ],
        'name' => [
            'title' => __('医师姓名'),
            'type' => 'text',
            'search' => true
        ],
        'certificate_number' => [
            'title' => __('医师证号'),
            'type' => 'text',
            'search' => true
        ],
        'hospital' => [
            'title' => __('执业医院'),
            'type' => 'text'
        ],
        'specialty' => [
            'title' => __('专业方向'),
            'type' => 'text'
        ],
        'status' => [
            'title' => __('认证状态'),
            'type' => 'select',
            'search' => true,
            'options' => [
                0 => __('未认证'),
                1 => __('待审核'),
                2 => __('已通过'),
                3 => __('已拒绝')
            ]
        ],
        'created_at' => [
            'title' => __('提交时间'),
            'type' => 'datetime',
            'search' => true
        ],
        'rejection_reason' => [
            'title' => __('拒绝原因'),
            'type' => 'textarea',
            'display' => false
        ],
        'certification_image' => [
            'title' => __('医师证照片'),
            'type' => 'image',
            'display' => true
        ]
    ],

    // 审核表单字段
    'audit' => [
        'status' => [
            'title' => __('认证状态'),
            'type' => 'radio',
            'options' => [
                2 => __('通过'),
                3 => __('拒绝')
            ]
        ],
        'rejection_reason' => [
            'title' => __('拒绝原因'),
            'type' => 'textarea',
            'display' => true,
            'rules' => 'required',
            'when' => [
                'status' => '3'
            ]
        ]
    ]
];
