<?php
namespace app\common\model;

class UserCertification extends Model
{
    // 表名
    protected $name = 'user_certification';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 追加属性
    protected $append = [];

    // 认证状态
    const STATUS_UNVERIFIED = 0;
    const STATUS_PENDING = 1;
    const STATUS_APPROVED = 2;
    const STATUS_REJECTED = 3;

    public function getStatusList()
    {
        return [
            self::STATUS_UNVERIFIED => '未认证',
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '已通过',
            self::STATUS_REJECTED => '已拒绝'
        ];
    }

    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id');
    }

    /**
     * 获取认证列表（管理员使用）
     */
    public static function getCertificationList($page = 1, $limit = 10)
    {
        return self::with('user')
            ->where('status', self::STATUS_PENDING)
            ->page($page, $limit)
            ->order('created_at', 'desc')
            ->select()
            ->toArray();
    }

    /**
     * 审核认证
     */
    public static function audit($id, $status, $reason = '')
    {
        $certification = self::where('id', $id)->find();
        if (!$certification) {
            return false;
        }

        $certification->status = $status;
        if ($status === self::STATUS_REJECTED) {
            $certification->rejection_reason = $reason;
        }

        return $certification->save();
    }
}
