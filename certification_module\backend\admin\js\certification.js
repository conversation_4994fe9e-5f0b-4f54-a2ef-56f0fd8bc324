// 认证管理js
layui.define(['table', 'form', 'upload'], function (exports) {
    var $ = layui.jquery;
    var table = layui.table;
    var form = layui.form;
    var upload = layui.upload;
    var laytpl = layui.laytpl;
    var layer = layui.layer;
    var laydate = layui.laydate;
    var element = layui.element;
    var layedit = layui.layedit;
    var laypage = layui.laypage;
    var admin = layui.admin;
    var view = layui.view;
    var upload = layui.upload;
    var setter = layui.setter;
    var device = layui.device;
    var $eb = parent.$eb;
    var tableIns;
    var tableData = {
        elem: '#List',
        url: '{:Url("index")}',
        page: true,
        cols: [[
            {type: "checkbox"},
            {field: 'id', title: 'ID', sort: true},
            {field: 'name', title: '医师姓名'},
            {field: 'certificate_number', title: '医师证号'},
            {field: 'hospital', title: '执业医院'},
            {field: 'specialty', title: '专业方向'},
            {field: 'status', title: '认证状态', templet: '#status'},
            {field: 'created_at', title: '提交时间'},
            {field: 'certification_image', title: '医师证照片', templet: '#imageTpl'},
            {field: 'right', title: '操作', align: 'center', toolbar: '#action'}
        ]],
        done: function (res, curr, count) {
            $eb.tableCheck();
        }
    };
    tableIns = table.render(tableData);
    //状态
    var statusTpl = '<div class="layui-btn-group">{{#  if(d.status == 0){ }}<button type="button" class="layui-btn layui-btn-normal layui-btn-xs">未认证</button>{{#  } else if(d.status == 1){ }}<button type="button" class="layui-btn layui-btn-warm layui-btn-xs">待审核</button>{{#  } else if(d.status == 2){ }}<button type="button" class="layui-btn layui-btn-normal layui-btn-xs">已通过</button>{{#  } else { }}<button type="button" class="layui-btn layui-btn-danger layui-btn-xs">已拒绝</button>{{#  } }}</div>';
    //图片显示
    var imageTpl = '<div class="layui-table-cell laytable-cell-1-image"><img src="{{d.certification_image}}" class="layui-upload-img"></div>';
    //操作
    var actionTpl = '<div class="layui-btn-group">{{#  if(d.status == 1){ }}<button type="button" class="layui-btn layui-btn-normal layui-btn-xs" onclick="$eb.createModalFrame(\'审核认证\',\'{:Url(\'audit\',array(\'id\'=>d.id))}\')"><i class="layui-icon">&#xe642;</i>审核</button>{{#  } }}</div>';
    //搜索
    var searchArr = [
        {field: 'name', title: '医师姓名', type: 'text'},
        {field: 'certificate_number', title: '医师证号', type: 'text'},
        {field: 'status', title: '认证状态', type: 'select', options: [
            {value: 0, title: '未认证'},
            {value: 1, title: '待审核'},
            {value: 2, title: '已通过'},
            {value: 3, title: '已拒绝'}
        ]}
    ];
    $eb.search(searchArr);
    //添加
    $('.add').click(function () {
        $eb.createModalFrame('添加认证', '{:Url("add")}', {
            width: '50%',
            height: '60%'
        });
    });
    //编辑
    $('.edit').click(function () {
        var checkStatus = table.checkStatus('List');
        var data = checkStatus.data;
        if (data.length != 1) {
            $eb.$alert('请选择一条数据');
            return;
        }
        $eb.createModalFrame('编辑认证', '{:Url("edit", array("id" => data[0].id))}', {
            width: '50%',
            height: '60%'
        });
    });
    //删除
    $('.del').click(function () {
        var checkStatus = table.checkStatus('List');
        var data = checkStatus.data;
        if (data.length == 0) {
            $eb.$alert('请选择要删除的数据');
            return;
        }
        $eb.$confirm('确定要删除选中的数据吗？', function () {
            var ids = [];
            $.each(data, function (index, item) {
                ids.push(item.id);
            });
            $eb.$swal("loading", "正在删除中");
            $.ajax({
                url: '{:Url("delete")}',
                type: 'POST',
                data: {ids: ids},
                success: function (res) {
                    if (res.code == 200) {
                        $eb.$swal("success", res.msg);
                        tableIns.reload();
                    } else {
                        $eb.$swal("error", res.msg);
                    }
                },
                error: function (res) {
                    $eb.$swal("error", res.msg);
                }
            });
        });
    });
    //导出
    $('.export').click(function () {
        var checkStatus = table.checkStatus('List');
        var data = checkStatus.data;
        if (data.length == 0) {
            $eb.$alert('请选择要导出的数据');
            return;
        }
        var ids = [];
        $.each(data, function (index, item) {
            ids.push(item.id);
        });
        window.location.href = '{:Url("export")}?ids=' + ids.join(',');
    });
    exports('certification', {});
});
