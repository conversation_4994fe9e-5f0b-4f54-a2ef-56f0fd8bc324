# 医师认证功能模块

## 功能说明

### 1. 认证状态
- 未认证 (0)
- 待审核 (1)
- 已通过 (2)
- 已拒绝 (3)

### 2. 认证流程
1. 用户提交认证信息
2. 管理员审核
3. 审核通过/拒绝
4. 用户收到通知

## 使用说明

### 1. 前端集成

1. 在项目中安装依赖
```bash
npm install
```

2. 在需要使用认证功能的页面中使用
```javascript
// 检查认证状态
async checkCertification() {
    try {
        const res = await this.$u.get('/api/user/certification');
        if (res.status !== 2) {
            uni.showModal({
                title: '认证提示',
                content: res.status === 0 
                    ? '您需要先完成医师认证才能开方'
                    : '您的认证正在审核中',
                showCancel: false,
                confirmText: '去认证',
                success: () => {
                    uni.navigateTo({
                        url: '/pages/user/certification'
                    });
                }
            });
            return false;
        }
        return true;
    } catch (error) {
        uni.showToast({
            title: '检查认证状态失败',
            icon: 'error'
        });
        return false;
    }
}
```

### 2. 后端API

1. 获取认证状态
```php
GET /api/user/certification
```

2. 提交认证信息
```php
POST /api/user/certification
```

3. 管理员审核
```php
POST /admin/user/certification/audit
```

## 数据库设计

```sql
-- 用户认证信息表
CREATE TABLE user_certification (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    name VARCHAR(50) NOT NULL COMMENT '医师姓名',
    certificate_number VARCHAR(50) NOT NULL COMMENT '医师证号',
    hospital VARCHAR(100) COMMENT '执业医院',
    specialty VARCHAR(50) COMMENT '专业方向',
    certification_image VARCHAR(255) COMMENT '医师证图片',
    status TINYINT DEFAULT 0 COMMENT '认证状态：0-未认证,1-待审核,2-已通过,3-已拒绝',
    rejection_reason TEXT COMMENT '拒绝原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医师认证信息表';
```

## 注意事项

1. 所有认证图片需要进行安全处理
2. 认证信息提交需要防重复提交
3. 审核操作需要记录日志
4. 敏感信息需要加密存储

## 安全措施

1. 图片上传限制
   - 图片大小限制
   - 图片格式验证
   - 图片水印
   - 图片存储加密

2. 防重复提交
   - 添加防重复提交标志
   - 设置提交冷却时间
   - 记录提交IP

3. 审核日志
   - 记录审核操作
   - 记录审核时间
   - 记录审核人
