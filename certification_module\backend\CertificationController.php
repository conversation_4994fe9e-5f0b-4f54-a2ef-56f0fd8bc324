<?php
namespace app\api\controller;

use app\api\controller\Base;
use app\common\model\UserCertification;
use think\facade\Request;

class CertificationController extends Base
{
    /**
     * 获取认证状态
     */
    public function status()
    {
        $user_id = $this->auth->id;
        $certification = UserCertification::where('user_id', $user_id)
            ->field('status, name, certificate_number, hospital, specialty, rejection_reason')
            ->find();
        
        if (!$certification) {
            return json([
                'status' => 0,
                'message' => '未认证'
            ]);
        }
        
        return json([
            'status' => $certification['status'],
            'certificationInfo' => [
                'name' => $certification['name'],
                'certificateNumber' => $certification['certificate_number'],
                'hospital' => $certification['hospital'],
                'specialty' => $certification['specialty'],
                'rejectionReason' => $certification['rejection_reason']
            ]
        ]);
    }

    /**
     * 提交认证信息
     */
    public function submit()
    {
        $data = Request::post();
        $user_id = $this->auth->id;
        
        // 验证数据
        $validate = validate('Certification');
        if (!$validate->check($data)) {
            return json(['code' => 400, 'msg' => $validate->getError()]);
        }
        
        // 处理图片上传
        if (isset($data['certificationImage'])) {
            $image = $this->handleImageUpload($data['certificationImage']);
            if (!$image) {
                return json(['code' => 400, 'msg' => '图片上传失败']);
            }
            $data['certification_image'] = $image;
        }
        
        $certification = UserCertification::where('user_id', $user_id)->find();
        
        if ($certification) {
            if ($certification['status'] === 2) {
                return json(['code' => 400, 'msg' => '已认证，无需重复提交']);
            }
            $certification->save($data);
        } else {
            $data['user_id'] = $user_id;
            $certification = UserCertification::create($data);
        }
        
        // 发送通知
        $this->sendNotification($user_id, '您的认证信息已提交，等待审核');
        
        return json(['code' => 200, 'msg' => '提交成功，等待审核']);
    }

    /**
     * 处理图片上传
     */
    private function handleImageUpload($image)
    {
        try {
            // 这里需要根据实际的图片上传逻辑来实现
            // 可能需要调用云存储服务或本地存储
            return 'image_url'; // 返回存储后的图片URL
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 发送通知
     */
    private function sendNotification($user_id, $content)
    {
        // 这里实现通知逻辑
        // 可以是短信、邮件或站内消息
    }
}
