{extend name="./layout/layout" /}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-btn-group">
                        <button type="button" class="layui-btn layui-btn-sm" onclick="$eb.createModalFrame('添加认证','{:Url('add')}')">
                            <i class="layui-icon">&#xe654;</i>添加认证
                        </button>
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <script type="text/html" id="status">
                        {{#  if(d.status == 0){ }}
                        <span class="layui-badge">未认证</span>
                        {{#  } else if(d.status == 1){ }}
                        <span class="layui-badge layui-bg-orange">待审核</span>
                        {{#  } else if(d.status == 2){ }}
                        <span class="layui-badge layui-bg-green">已通过</span>
                        {{#  } else { }}
                        <span class="layui-badge layui-bg-red">已拒绝</span>
                        {{#  } }}
                    </script>
                    <script type="text/html" id="action">
                        {{#  if(d.status == 1){ }}
                        <button type="button" class="layui-btn layui-btn-normal layui-btn-xs" onclick="$eb.createModalFrame('审核认证','{:Url('audit',array('id'=>d.id))}')">
                            <i class="layui-icon">&#xe642;</i>审核
                        </button>
                        {{#  } }}
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
layui.use(['table','form'], function () {
    var $ = layui.jquery;
    var form = layui.form;
    var table = layui.table;

    table.render({
        elem: '#List',
        url: '{:Url("index")}',
        page: true,
        cols: [[
            {type: "checkbox"},
            {field: 'id', title: 'ID', sort: true},
            {field: 'name', title: '医师姓名'},
            {field: 'certificate_number', title: '医师证号'},
            {field: 'hospital', title: '执业医院'},
            {field: 'specialty', title: '专业方向'},
            {field: 'status', title: '认证状态', templet: '#status'},
            {field: 'created_at', title: '提交时间'},
            {field: 'certification_image', title: '医师证照片', templet: '#imageTpl'},
            {field: 'right', title: '操作', align: 'center', toolbar: '#action'}
        ]]
    });

    //图片显示模板
    var imageTpl = '<div class="layui-table-cell laytable-cell-1-image"><img src="{{d.certification_image}}" class="layui-upload-img"></div>';
    form.render();
});
</script>
{/block}
