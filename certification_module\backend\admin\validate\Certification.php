<?php
namespace app\admin\validate;

use think\Validate;

class Certification extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'name' => 'require|max:50',
        'certificate_number' => 'require|max:50',
        'hospital' => 'max:100',
        'specialty' => 'max:50',
        'status' => 'require|in:0,1,2,3',
        'rejection_reason' => 'max:255',
        'certification_image' => 'require',
    ];

    /**
     * 提示消息
     */
    protected $message = [
        'name.require' => '医师姓名不能为空',
        'name.max' => '医师姓名最多不能超过50个字符',
        'certificate_number.require' => '医师证号不能为空',
        'certificate_number.max' => '医师证号最多不能超过50个字符',
        'hospital.max' => '执业医院最多不能超过100个字符',
        'specialty.max' => '专业方向最多不能超过50个字符',
        'status.require' => '认证状态不能为空',
        'status.in' => '认证状态值不正确',
        'rejection_reason.max' => '拒绝原因最多不能超过255个字符',
        'certification_image.require' => '医师证照片不能为空',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'add' => ['name', 'certificate_number', 'hospital', 'specialty', 'certification_image'],
        'edit' => ['name', 'certificate_number', 'hospital', 'specialty', 'certification_image'],
        'audit' => ['status', 'rejection_reason'],
    ];
}
