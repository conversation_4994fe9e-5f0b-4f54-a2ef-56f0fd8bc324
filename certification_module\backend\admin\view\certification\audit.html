{extend name="./layout/layout" /}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <form class="layui-form" action="" lay-filter="layuiadmin-form-admin" id="layuiadmin-form-admin">
                        <input type="hidden" name="id" value="{$id|default=''}">
                        <div class="layui-form-item">
                            <label class="layui-form-label">医师姓名</label>
                            <div class="layui-input-block">
                                <input type="text" name="name" required lay-verify="required" placeholder="请输入医师姓名" autocomplete="off" class="layui-input" value="{$row.name|default=''}">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">医师证号</label>
                            <div class="layui-input-block">
                                <input type="text" name="certificate_number" required lay-verify="required" placeholder="请输入医师证号" autocomplete="off" class="layui-input" value="{$row.certificate_number|default=''}">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">执业医院</label>
                            <div class="layui-input-block">
                                <input type="text" name="hospital" placeholder="请输入执业医院" autocomplete="off" class="layui-input" value="{$row.hospital|default=''}">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">专业方向</label>
                            <div class="layui-input-block">
                                <input type="text" name="specialty" placeholder="请输入专业方向" autocomplete="off" class="layui-input" value="{$row.specialty|default=''}">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">医师证照片</label>
                            <div class="layui-input-block">
                                <img src="{$row.certification_image|default=''}" class="layui-upload-img" style="width: 200px; height: 200px;">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">认证状态</label>
                            <div class="layui-input-block">
                                <select name="status" lay-verify="required">
                                    <option value="2">通过</option>
                                    <option value="3">拒绝</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item layui-hide">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-block">
                                <button type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="formSubmit">确认保存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
layui.use(['form'], function () {
    var form = layui.form;

    //监听提交
    form.on('submit(formSubmit)', function (data) {
        var index = parent.layer.getFrameIndex(window.name);
        $eb.$swal("loading", "正在提交中");
        $.ajax({
            url: "{:Url('audit')}",
            type: 'POST',
            data: data.field,
            success: function (res) {
                parent.layer.close(index);
                if (res.code == 200) {
                    $eb.$swal("success", res.msg);
                    parent.location.reload();
                } else {
                    $eb.$swal("error", res.msg);
                }
            },
            error: function (res) {
                parent.layer.close(index);
                $eb.$swal("error", res.msg);
            }
        });
        return false;
    });
});
</script>
{/block}
