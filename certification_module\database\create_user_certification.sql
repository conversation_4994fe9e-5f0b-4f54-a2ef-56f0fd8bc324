-- 创建医师认证信息表
CREATE TABLE user_certification (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    name VARCHAR(50) NOT NULL COMMENT '医师姓名',
    certificate_number VARCHAR(50) NOT NULL COMMENT '医师证号',
    hospital VARCHAR(100) COMMENT '执业医院',
    specialty VARCHAR(50) COMMENT '专业方向',
    certification_image VARCHAR(255) COMMENT '医师证图片',
    status TINYINT DEFAULT 0 COMMENT '认证状态：0-未认证,1-待审核,2-已通过,3-已拒绝',
    rejection_reason TEXT COMMENT '拒绝原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医师认证信息表';

-- 添加索引
ALTER TABLE user_certification ADD INDEX idx_user_id (user_id);
ALTER TABLE user_certification ADD INDEX idx_status (status);
ALTER TABLE user_certification ADD INDEX idx_created_at (created_at);
